"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/use-auth";
import { User, Mail, Calendar, Settings, Bell, Shield } from "lucide-react";

/**
 * Example component demonstrating how to use the new auth and user detail system
 */
export function UserProfileCard() {
  const { 
    user, 
    isAuthenticated, 
    userDetail, 
    updatePreferences, 
    updateSettings 
  } = useAuth();

  if (!isAuthenticated || !user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">Please log in to view your profile</p>
        </CardContent>
      </Card>
    );
  }

  const handleNotificationToggle = (checked: boolean) => {
    updatePreferences({ notifications: checked });
  };

  const handleEmailNotificationToggle = (checked: boolean) => {
    updateSettings({ emailNotifications: checked });
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'system') => {
    updatePreferences({ theme });
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={user.image || undefined} alt={user.name} />
            <AvatarFallback>
              {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <CardTitle className="text-lg">{user.name}</CardTitle>
            <CardDescription className="flex items-center gap-1">
              <Mail className="size-3" />
              {user.email}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* User Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <User className="size-4" />
            <span className="text-sm">Status</span>
          </div>
          <Badge variant={user.emailVerified ? "default" : "secondary"}>
            {user.emailVerified ? "Verified" : "Unverified"}
          </Badge>
        </div>

        {/* Subscription */}
        {user.subscription && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="size-4" />
              <span className="text-sm">Plan</span>
            </div>
            <Badge variant="outline">{user.subscription}</Badge>
          </div>
        )}

        {/* Member Since */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="size-4" />
            <span className="text-sm">Member since</span>
          </div>
          <span className="text-sm text-muted-foreground">
            {new Date(user.createdAt).toLocaleDateString()}
          </span>
        </div>

        {/* Preferences */}
        {userDetail && (
          <div className="space-y-3 pt-4 border-t">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Settings className="size-4" />
              Preferences
            </h4>
            
            {/* Notifications */}
            <div className="flex items-center justify-between">
              <Label htmlFor="notifications" className="text-sm">
                Push Notifications
              </Label>
              <Switch
                id="notifications"
                checked={userDetail.preferences?.notifications || false}
                onCheckedChange={handleNotificationToggle}
              />
            </div>

            {/* Email Notifications */}
            <div className="flex items-center justify-between">
              <Label htmlFor="email-notifications" className="text-sm">
                Email Notifications
              </Label>
              <Switch
                id="email-notifications"
                checked={userDetail.settings?.emailNotifications || false}
                onCheckedChange={handleEmailNotificationToggle}
              />
            </div>

            {/* Theme Selection */}
            <div className="space-y-2">
              <Label className="text-sm">Theme</Label>
              <div className="flex gap-2">
                {(['light', 'dark', 'system'] as const).map((theme) => (
                  <Button
                    key={theme}
                    variant={userDetail.preferences?.theme === theme ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleThemeChange(theme)}
                    className="capitalize"
                  >
                    {theme}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}