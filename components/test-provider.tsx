"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useUserDetail } from "@/context/UserDetailContext";

/**
 * Test component to verify the provider system is working correctly
 * This can be used for debugging and testing purposes
 */
export function TestProvider() {
  const auth = useAuth();
  const userDetailContext = useUserDetail();

  const testUpdatePreferences = () => {
    auth.updatePreferences({
      theme: 'dark',
      notifications: true,
      language: 'en'
    });
  };

  const testUpdateProfile = () => {
    auth.updateProfile({
      bio: 'Test bio updated at ' + new Date().toLocaleTimeString(),
      location: 'Test Location'
    });
  };

  const testClearDetails = () => {
    auth.clearUserDetail();
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Provider System Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Auth State */}
        <div>
          <h3 className="font-semibold mb-2">Authentication State</h3>
          <div className="text-sm space-y-1">
            <p>Authenticated: {auth.isAuthenticated ? 'Yes' : 'No'}</p>
            <p>Pending: {auth.isPending ? 'Yes' : 'No'}</p>
            <p>User ID: {auth.user?.id || 'None'}</p>
            <p>User Name: {auth.user?.name || 'None'}</p>
            <p>User Email: {auth.user?.email || 'None'}</p>
          </div>
        </div>

        {/* User Details */}
        <div>
          <h3 className="font-semibold mb-2">User Details</h3>
          <div className="text-sm">
            <pre className="bg-muted p-2 rounded text-xs overflow-auto">
              {JSON.stringify(auth.userDetail, null, 2)}
            </pre>
          </div>
        </div>

        {/* Context Direct Access */}
        <div>
          <h3 className="font-semibold mb-2">Direct Context Access</h3>
          <div className="text-sm">
            <pre className="bg-muted p-2 rounded text-xs overflow-auto">
              {JSON.stringify(userDetailContext.userDetail, null, 2)}
            </pre>
          </div>
        </div>

        {/* Test Actions */}
        {auth.isAuthenticated && (
          <div className="space-y-2">
            <h3 className="font-semibold">Test Actions</h3>
            <div className="flex flex-wrap gap-2">
              <Button size="sm" onClick={testUpdatePreferences}>
                Update Preferences
              </Button>
              <Button size="sm" onClick={testUpdateProfile}>
                Update Profile
              </Button>
              <Button size="sm" variant="outline" onClick={testClearDetails}>
                Clear Details
              </Button>
              <Button size="sm" variant="destructive" onClick={auth.signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {auth.error && (
          <div>
            <h3 className="font-semibold mb-2 text-destructive">Error</h3>
            <div className="text-sm text-destructive">
              <pre className="bg-destructive/10 p-2 rounded text-xs overflow-auto">
                {JSON.stringify(auth.error, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
