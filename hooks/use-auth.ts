"use client";

import { authClient } from "@/lib/auth-client";
import { useUserDetail, type UserDetails } from "@/context/UserDetailContext";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useCallback } from "react";

/**
 * Custom hook that combines Better Auth session management with user details
 * Provides a unified interface for authentication and user data management
 */
export function useAuth() {
  const { data: session, isPending, error } = authClient.useSession();
  const { userDetail, updateUserDetail, clearUserDetail } = useUserDetail();
  const router = useRouter();

  // Check if user is authenticated
  const isAuthenticated = !!session?.user;

  // Get current user
  const user = session?.user || null;

  // Sign out function with proper cleanup
  const signOut = useCallback(async () => {
    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            clearUserDetail();
            toast.success("Logged out successfully");
            router.push("/");
          },
          onError: (context) => {
            console.error("Sign out error:", context.error);
            toast.error("Failed to log out");
          }
        }
      });
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to log out");
    }
  }, [clearUserDetail, router]);

  // Update user preferences
  const updatePreferences = useCallback((preferences: Partial<NonNullable<UserDetails['preferences']>>) => {
    updateUserDetail({ preferences });
  }, [updateUserDetail]);

  // Update user profile
  const updateProfile = useCallback((profile: Partial<NonNullable<UserDetails['profile']>>) => {
    updateUserDetail({ profile });
  }, [updateUserDetail]);

  // Update user settings
  const updateSettings = useCallback((settings: Partial<NonNullable<UserDetails['settings']>>) => {
    updateUserDetail({ settings });
  }, [updateUserDetail]);

  return {
    // Auth state
    user,
    session,
    isAuthenticated,
    isPending,
    error,
    
    // User details
    userDetail,
    
    // Actions
    signOut,
    updateUserDetail,
    updatePreferences,
    updateProfile,
    updateSettings,
    clearUserDetail,
  };
}