import { createContext, useContext } from "react";

// Define the user type based on Prisma schema and Better Auth
export interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  subscription?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define the session type that Better Auth provides
export interface Session {
  user: User;
  session: {
    id: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    ipAddress?: string | null;
    userAgent?: string | null;
    userId: string;
  };
}

// Define additional user details that might be needed in the app
export interface UserDetails {
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    notifications?: boolean;
    language?: string;
  };
  profile?: {
    bio?: string;
    location?: string;
    website?: string;
  };
  settings?: {
    twoFactorEnabled?: boolean;
    emailNotifications?: boolean;
  };
}

// Context value type
export interface UserDetailContextValue {
  userDetail: UserDetails | null;
  setUserDetail: (details: UserDetails | null) => void;
  updateUserDetail: (updates: Partial<UserDetails>) => void;
  clearUserDetail: () => void;
}

// Create the context with proper typing
export const UserDetailContext = createContext<UserDetailContextValue | null>(null);

// Custom hook to use the context with proper error handling
export const useUserDetail = (): UserDetailContextValue => {
  const context = useContext(UserDetailContext);

  if (!context) {
    throw new Error('useUserDetail must be used within a UserDetailProvider');
  }

  return context;
};