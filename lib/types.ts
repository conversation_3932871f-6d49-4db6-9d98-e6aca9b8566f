// Re-export types from context for easier imports
export type {
  User,
  Session,
  UserDetails,
  UserDetailContextValue,
} from "@/context/UserDetailContext";

// Additional app-specific types can be added here
export interface AppConfig {
  name: string;
  version: string;
  features: {
    darkMode: boolean;
    notifications: boolean;
    analytics: boolean;
  };
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form types
export interface LoginFormData {
  email: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  disabled?: boolean;
  external?: boolean;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Subscription types
export type SubscriptionTier = 'free' | 'pro' | 'enterprise';

export interface SubscriptionInfo {
  tier: SubscriptionTier;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
}
