import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
    server: {
        DATABASE_URL: z.string().url(),
        AUTH_GITHUB_CLIENT_ID: z.string().min(1),
        AUTH_GITHUB_CLIENT_SECRET: z.string().min(1),
        AUTH_GOOGLE_CLIENT_ID: z.string().min(1),
        AUTH_GOOGLE_CLIENT_SECRET: z.string().min(1),
        RESEND_API_KEY: z.string().min(1),
        ARCJET_KEY: z.string().min(1),
    },

    experimental__runtimeEnv: {
    },

});