import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { emailOTP } from "better-auth/plugins";
import {prisma} from "@/lib/db";
import {env} from "@/lib/env";
import { resend } from "./resend";

export const auth = betterAuth({
    database: prismaAdapter(prisma, {
        provider: "postgresql", // or "mysql", "postgresql", ...etc
    }),
    socialProviders: {
        github: {
            clientId: env.AUTH_GITHUB_CLIENT_ID,
            clientSecret: env.AUTH_GITHUB_CLIENT_SECRET,
        },
        google: {
            clientId: env.AUTH_GOOGLE_CLIENT_ID,
            clientSecret: env.AUTH_GOOGLE_CLIENT_SECRET,
        },
    },
    plugins: [
        emailOTP({
            async sendVerificationOTP({ email, otp}) {
                 await resend.emails.send({
                     from: "<PERSON>gi <<EMAIL>>",
                     to: [email],
                     subject: "Digi - Email Verification",
                     html: `<p>Your verification code is <strong>${otp}</strong>. Please enter this code to verify your email address.</p>`,
                 })
            }
        })
    ]
});