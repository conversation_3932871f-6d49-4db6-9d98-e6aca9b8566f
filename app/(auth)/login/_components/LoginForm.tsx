"use client";

import {<PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle} from "@/components/ui/card";
import {<PERSON><PERSON>} from "@/components/ui/button";
import {Loader, Send} from "lucide-react";
import {FaGithub, FaGoogle} from "react-icons/fa";
import {Label} from "@/components/ui/label";
import {Input} from "@/components/ui/input";
import {useState, useTransition} from "react";
import {authClient} from "@/lib/auth-client";
import {toast} from "sonner";
import {useRouter} from "next/navigation";

export function LoginForm(){
    const [isGithubPending, startGithubTransition] = useTransition()
    const [isGooglePending, startGoogleTransition] = useTransition()
    const [isEmailPending, startEmailTransition] = useTransition()

    const [email, setEmail] = useState("");
    const router = useRouter()


    async function githubLogin() {
        startGithubTransition(async () => {
            await authClient.signIn.social({
                provider: "github",
                callbackURL: "/",
                fetchOptions: {
                    onSuccess: () => {
                        toast.success("Logged in successfully with Github")
                    },
                    onError: () => {
                        toast.error("Failed to login with Github")
                    }
                }
            })
        })
    }

    async function googleLogin() {
        startGoogleTransition(async () => {
            await authClient.signIn.social({
                provider: "google",
                callbackURL: "/",
                fetchOptions: {
                    onSuccess: () => {
                        toast.success("Logged in successfully with Google")
                    },
                    onError: () => {
                        toast.error("Failed to login with Google")
                    }
                }
            })
        })
    }

    function emailLogin() {
        startEmailTransition( async () => {
            await authClient.emailOtp.sendVerificationOtp({
                email: email,
                type: "sign-in",
                fetchOptions: {
                    onSuccess: () => {
                        toast.success("Verification code sent to your email")
                        router.push(`/verify-email?email=${email}`)
                    },
                    onError: () => {
                        toast.error("Failed to send verification code")
                    }
                }
            })
        })
    }
    
    return (
        <Card>
            <CardHeader className={"text-center"}>
                <CardTitle>Welcome Back 👋</CardTitle>
                <CardDescription className={"text-muted-foreground"}>Login with your Github or Google account
                    below</CardDescription>
            </CardHeader>
            <CardContent className={"space-y-2"}>
                <Button className={"w-full hover:cursor-pointer"} variant={"outline"} onClick={githubLogin}
                        disabled={isGithubPending}>
                    {isGithubPending ? (
                        <>
                            <Loader className={"size-4 animate-spin"}/>
                            <span>logging in....</span>
                        </>
                    ) : (
                        <>
                            <FaGithub className={"size-4"}/>
                            <span>continue with Github</span>
                        </>
                    )}
                </Button>
                <Button className={"w-full hover:cursor-pointer"} onClick={googleLogin} disabled={isGooglePending}>
                    {isGooglePending ? (
                        <>
                            <Loader className={"size-4 animate-spin"}/>
                            <span>logging in....</span>
                        </>
                    ) : (
                        <>
                            <FaGoogle className={"size-4"}/>
                            <span>continue with Google</span>
                        </>
                    )}
                </Button>
                <div
                    className={"relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border"}>
                    <span className={"relative z-10 bg-card px-2 text-muted-foreground"}>or continue with</span>
                </div>
                <div className={"grid gap-3"}>
                    <div className={"grid gap-2"}>
                        <Label htmlFor={"email"}>Email</Label>
                        <Input
                            type={"email"}
                            placeholder={"enter your email"}
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                        />
                    </div>
                    <Button className={"w-full hover:cursor-pointer"} onClick={emailLogin} disabled={isEmailPending}>
                        {isEmailPending ? (
                            <>
                                <Loader className={"size-4 animate-spin"}/>
                                <span>sending verification code...</span>
                            </>
                        ) : (
                            <>
                                <Send className={"size-4"}/>
                                <span>send verification code</span>
                            </>
                        )}
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}