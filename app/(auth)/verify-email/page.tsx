"use client";

import {<PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle} from "@/components/ui/card";
import {InputOTP, InputOTPGroup, InputOTPSlot} from "@/components/ui/input-otp";
import {useState, useTransition} from "react";
import {Button} from "@/components/ui/button";
import {authClient} from "@/lib/auth-client";
import {useRouter, useSearchParams} from "next/navigation";
import {toast} from "sonner";
import {Loader} from "lucide-react";
import {MdOutlineMail} from "react-icons/md";

export default function VerifyEmail() {
    const [otp, setOtp] = useState("");
    const router = useRouter()
    const params = useSearchParams()
    const email = params.get("email") as string;

    const [isEmailPending, startEmailTransition] = useTransition();

    function verifyOTP() {
        startEmailTransition( async () => {
            await authClient.signIn.emailOtp({
                email: email,
                otp: otp,
                fetchOptions: {
                    onSuccess: () => {
                        toast.success("Email verified successfully");
                        router.push("/");
                    },
                    onError: () => {
                        toast.error("Failed to verify email")
                    }
                }
            });
        })
    }

    return (
        <Card className={"w-full mx-auto"}>
            <CardHeader className={"text-center"}>
                <CardTitle className={"text-xl"}>Please check your email</CardTitle>
                <CardDescription>We have sent a verification email code to your email address. Please open the email and paste the code below</CardDescription>
            </CardHeader>
            <CardContent className={"space-y-6"}>
                <div className={"flex flex-col items-center space-y-2"}>
                    <InputOTP maxLength={6} className={"gap-2"} value={otp} onChange={(value) => setOtp(value)}>
                        <InputOTPGroup>
                            <InputOTPSlot index={0} />
                            <InputOTPSlot index={1} />
                            <InputOTPSlot index={2} />
                        </InputOTPGroup>
                        <InputOTPGroup>
                            <InputOTPSlot index={3} />
                            <InputOTPSlot index={4} />
                            <InputOTPSlot index={5} />
                        </InputOTPGroup>
                    </InputOTP>
                    <p className={"text-sm text-muted-foreground"}>Enter the 6-digit code sent to your email</p>
                </div>
                <Button className={"w-full mt-4"} onClick={verifyOTP} disabled={isEmailPending || otp.length !== 6}>
                    {isEmailPending ? (
                        <>
                            <Loader className={"size-4 animate-spin"}/>
                            <span >Verifying...</span>
                        </>
                    ) : (
                        <>
                            <MdOutlineMail className={"size-4"}/>
                            <span>Verify Email</span>
                        </>
                    )}
                </Button>
            </CardContent>
        </Card>
    )
}