"use client";

import {ThemeToggle} from "@/components/ui/theme-toggle";
import {Button, buttonVariants} from "@/components/ui/button";
import {authClient} from "@/lib/auth-client";
import {toast} from "sonner";
import {useRouter} from "next/navigation";
import Link from "next/link";
import {ArrowRight} from "lucide-react";


export default function Home() {
    const {data: session,} = authClient.useSession();
    const router = useRouter()

    async function logOut() {
        await authClient.signOut({
            fetchOptions: {
                onSuccess: () => {
                    toast.success("Logged out successfully");
                    router.push("/")
                },
                onError: () => {
                    toast.error("Failed to log out:");
                }
            }
        })
    }

    return (
        <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
            <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
                <ThemeToggle/>
                {session ? (
                    <div className={"flex flex-col gap-4"}>
                        <p>{session.user.name}</p>
                        <Button onClick={logOut}>
                            logout
                            <ArrowRight className={"size-4 ml-2"}/>
                        </Button>
                    </div>
                ) : (
                    <Link href={"/login"} className={buttonVariants({
                        size: "lg",
                        variant: "outline"
                    })}>
                        Login
                        <ArrowRight className={"size-4 ml-2"}/>
                    </Link>
                )}
            </main>
        </div>
    );
}
