"use client";

import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Button, buttonVariants } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { UserProfileCard } from "@/components/user-profile-card";
import Link from "next/link";
import { ArrowR<PERSON>, User, Settings } from "lucide-react";

export default function Home() {
    const { user, isAuthenticated, signOut, userDetail } = useAuth();

    return (
        <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
            <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
                <ThemeToggle/>
                {isAuthenticated ? (
                    <div className={"flex flex-col gap-6 items-center sm:items-start"}>
                        <div className="text-center sm:text-left">
                            <h1 className="text-2xl font-bold">Welcome back, {user?.name}!</h1>
                            <p className="text-muted-foreground">Manage your account and preferences below.</p>
                        </div>

                        <UserProfileCard />
                    </div>
                ) : (
                    <div className="text-center space-y-4">
                        <div>
                            <h1 className="text-3xl font-bold">Welcome to Digi</h1>
                            <p className="text-muted-foreground">Your modern Next.js starter kit</p>
                        </div>
                        <Link href={"/login"} className={buttonVariants({
                            size: "lg",
                            variant: "outline"
                        })}>
                            Login
                            <ArrowRight className={"size-4 ml-2"}/>
                        </Link>
                    </div>
                )}
            </main>
        </div>
    );
}