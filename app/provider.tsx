"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { UserDetailContext, UserDetails, UserDetailContextValue } from "@/context/UserDetailContext";
import { authClient } from "@/lib/auth-client";

interface UserDetailProviderProps {
  children: React.ReactNode;
}

export function UserDetailProvider({ children }: UserDetailProviderProps) {
  const [userDetail, setUserDetail] = useState<UserDetails | null>(null);
  const { data: session } = authClient.useSession();

  // Update user detail with partial updates
  const updateUserDetail = useCallback((updates: Partial<UserDetails>) => {
    setUserDetail(prev => {
      if (!prev) {
        return updates as UserDetails;
      }

      return {
        ...prev,
        preferences: {
          ...prev.preferences,
          ...updates.preferences,
        },
        profile: {
          ...prev.profile,
          ...updates.profile,
        },
        settings: {
          ...prev.settings,
          ...updates.settings,
        },
      };
    });
  }, []);

  // Clear user detail
  const clearUserDetail = useCallback(() => {
    setUserDetail(null);
  }, []);

  // Initialize user details when session changes
  useEffect(() => {
    if (session?.user) {
      // Initialize with default values if no user detail exists
      if (!userDetail) {
        setUserDetail({
          preferences: {
            theme: 'system',
            notifications: true,
            language: 'en',
          },
          profile: {},
          settings: {
            twoFactorEnabled: false,
            emailNotifications: true,
          },
        });
      }
    } else {
      // Clear user details when user logs out
      clearUserDetail();
    }
  }, [session?.user, userDetail, clearUserDetail]);

  // Context value
  const contextValue: UserDetailContextValue = {
    userDetail,
    setUserDetail,
    updateUserDetail,
    clearUserDetail,
  };

  return (
    <UserDetailContext.Provider value={contextValue}>
      {children}
    </UserDetailContext.Provider>
  );
}

// Export as default for backward compatibility
export default UserDetailProvider;