"use client";

import React, {useState} from 'react'
import {UserDetailContext} from "@/context/UserDetailContext";

function Provider({children}: Readonly<{ children: React.ReactNode }>) {
    const [userDetail, setUserDetail] = useState<any>();

    return (
        <UserDetailContext.Provider value={{userDetail, setUserDetail}}>
            <div>
                {children}
            </div>
        </UserDetailContext.Provider>
    )
}

export default Provider