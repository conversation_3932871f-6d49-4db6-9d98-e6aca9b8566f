# User Provider System Guide

This guide explains how to use the improved user provider system in the Digi application.

## Overview

The user provider system has been completely rewritten with proper TypeScript support and Better Auth integration. It provides a centralized way to manage user authentication state and user-specific details throughout the application.

## Architecture

### Core Components

1. **UserDetailContext** (`context/UserDetailContext.tsx`)
   - Defines TypeScript interfaces for User, Session, and UserDetails
   - Provides the React context and custom hook
   - Includes proper error handling

2. **UserDetailProvider** (`app/provider.tsx`)
   - Main provider component that wraps the application
   - Integrates with Better Auth session management
   - Manages user details state with automatic initialization

3. **useAuth Hook** (`hooks/use-auth.ts`)
   - Custom hook that combines Better Auth with user details
   - Provides a unified interface for authentication operations
   - Includes helper functions for updating user preferences

## Usage

### Basic Authentication Check

```tsx
import { useAuth } from "@/hooks/use-auth";

function MyComponent() {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  return <div>Welcome, {user.name}!</div>;
}
```

### Managing User Details

```tsx
import { useAuth } from "@/hooks/use-auth";

function UserSettings() {
  const { userDetail, updatePreferences, updateProfile } = useAuth();

  const handleThemeChange = (theme: 'light' | 'dark' | 'system') => {
    updatePreferences({ theme });
  };

  const handleProfileUpdate = (bio: string) => {
    updateProfile({ bio });
  };

  return (
    <div>
      <p>Current theme: {userDetail?.preferences?.theme}</p>
      <button onClick={() => handleThemeChange('dark')}>
        Switch to Dark Mode
      </button>
    </div>
  );
}
```

### Sign Out

```tsx
import { useAuth } from "@/hooks/use-auth";

function LogoutButton() {
  const { signOut } = useAuth();

  return (
    <button onClick={signOut}>
      Logout
    </button>
  );
}
```

## TypeScript Interfaces

### User
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  subscription?: string | null;
  createdAt: Date;
  updatedAt: Date;
}
```

### UserDetails
```typescript
interface UserDetails {
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    notifications?: boolean;
    language?: string;
  };
  profile?: {
    bio?: string;
    location?: string;
    website?: string;
  };
  settings?: {
    twoFactorEnabled?: boolean;
    emailNotifications?: boolean;
  };
}
```

## Available Hooks and Functions

### useAuth()
Returns an object with:
- `user`: Current user object or null
- `session`: Full Better Auth session
- `isAuthenticated`: Boolean indicating if user is logged in
- `isPending`: Boolean indicating if auth state is loading
- `userDetail`: User details object
- `signOut()`: Function to sign out the user
- `updateUserDetail()`: Function to update user details
- `updatePreferences()`: Helper to update preferences
- `updateProfile()`: Helper to update profile
- `updateSettings()`: Helper to update settings

### useUserDetail()
Direct access to the user detail context (lower level):
- `userDetail`: User details object
- `setUserDetail()`: Set user details
- `updateUserDetail()`: Update user details
- `clearUserDetail()`: Clear user details

## Example Components

### UserProfileCard
A complete example component (`components/user-profile-card.tsx`) that demonstrates:
- Displaying user information
- Managing preferences with checkboxes
- Theme switching
- Conditional rendering based on auth state

### Usage in Pages
```tsx
import { UserProfileCard } from "@/components/user-profile-card";

export default function ProfilePage() {
  return (
    <div className="container mx-auto p-4">
      <h1>User Profile</h1>
      <UserProfileCard />
    </div>
  );
}
```

## Integration with Better Auth

The provider automatically:
- Syncs with Better Auth session state
- Initializes user details when user logs in
- Clears user details when user logs out
- Provides type-safe access to user data

## Best Practices

1. **Always use the useAuth hook** for authentication-related operations
2. **Check isAuthenticated** before accessing user data
3. **Use the helper functions** (updatePreferences, updateProfile) instead of direct state updates
4. **Handle loading states** using the isPending flag
5. **Type your components** using the provided interfaces

## Migration from Old System

If you were using the old provider system:

1. Replace `authClient.useSession()` with `useAuth()`
2. Replace direct context usage with the new hooks
3. Update TypeScript types to use the new interfaces
4. Use the new helper functions for state updates

## Error Handling

The system includes proper error handling:
- Context usage outside provider throws descriptive errors
- Auth operations include error callbacks
- TypeScript ensures type safety throughout
